'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Github, Linkedin, Facebook, Instagram } from 'lucide-react'




export default function Footer() {
  const currentYear = new Date().getFullYear()

  const socialLinks = [
    {
      name: 'GitHub',
      href: 'https://github.com/ernstrmlo',
      icon: Github
    },
    {
      name: 'LinkedIn',
      href: 'https://linkedin.com/in/ernstrmlo',
      icon: Linkedin
    },
    {
      name: 'Facebook',
      href: 'https://facebook.com/ernstrmlo',
      icon: Facebook
    },
    {
      name: 'Instagram',
      href: 'https://instagram.com/ernstrmlo',
      icon: Instagram
    }
  ]

  return (
    <footer className="bg-transparent mt-20 relative z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col items-center space-y-8">
          {/* Avatar */}
          <div className="relative w-16 h-16 rounded-full overflow-hidden ring-2 ring-primary-200 dark:ring-primary-600">
            <Image
              src="/images/avatar.jpg"
              alt="<PERSON>lo"
              width={64}
              height={64}
              className="object-cover rounded-full"
              priority
            />
          </div>

          {/* Tagline */}
          <div className="text-center max-w-2xl">
            <p className="text-lg text-primary-600 dark:text-primary-400 font-medium mb-2">
              Ernst Romelo
            </p>
            <p className="text-primary-500 dark:text-primary-300 leading-relaxed">
              Crafting intelligent solutions through AI automation, modern web development,
              and innovative app experiences that transform ideas into digital reality.
            </p>
          </div>

          {/* Social Media Links */}
          <div className="flex items-center justify-center space-x-6">
            {socialLinks.map((social) => {
              const IconComponent = social.icon
              return (
                <Link
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="no-link-style text-primary-600 dark:text-primary-400 hover:text-primary-400 dark:hover:text-primary-300 transition-colors duration-300"
                  aria-label={social.name}
                >
                  <IconComponent className="w-6 h-6" />
                </Link>
              )
            })}
          </div>

          {/* Copyright */}
          <div className="text-center pt-8 border-t border-neutral-200 dark:border-neutral-700 w-full">
            <p className="text-sm text-primary-400 dark:text-primary-500">
              © 2021 - {currentYear} Ernst Romelo. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
